# 节点对接文档 (Node Integration Guide)

## 概述

本文档描述了如何通过Tunnel模块与网关进行通信，实现设备注册、心跳上报和模型上报功能。所有通信都基于WebSocket连接和结构化的消息格式。

## 连接建立

### WebSocket连接
```javascript
const ws = new WebSocket('ws://gateway-host:port');
```

### 消息格式
所有消息都遵循以下基础格式：
```typescript
{
  from: string,      // 发送方ID
  to: string,        // 接收方ID  
  type: string,      // 消息类型
  payload: object    // 消息载荷
}
```

## 1. 设备注册 (Device Registration)

### 请求消息
**消息类型**: `device_register_request`

```typescript
{
  from: "device-id",
  to: "gateway",
  type: "device_register_request",
  payload: {
    code: string,                    // 一次性注册码
    gateway_address: string,         // 网关地址
    reward_address: string,          // 奖励地址
    device_type?: string,           // 设备类型 (可选)
    gpu_type?: string,              // GPU类型 (可选)
    ip?: string,                    // IP地址 (可选)
    local_models?: object           // 本地模型信息 (可选)
  }
}
```

### 响应消息
**消息类型**: `device_register_response`

```typescript
{
  from: "gateway",
  to: "device-id",
  type: "device_register_response",
  payload: {
    device_id: string,              // 设备ID
    status: "connected" | "failed", // 注册状态
    device_type?: string,           // 设备类型
    reward_address?: string,        // 奖励地址
    error?: string                  // 错误信息 (失败时)
  }
}
```

### 示例代码
```javascript
// 发送注册请求
const registerMessage = {
  from: "my-device-id",
  to: "gateway",
  type: "device_register_request",
  payload: {
    code: "ABC123DEF456",
    gateway_address: "0x1234567890abcdef",
    reward_address: "0xfedcba0987654321",
    device_type: "gpu-miner",
    gpu_type: "RTX4090",
    ip: "*************"
  }
};

ws.send(JSON.stringify(registerMessage));
```

## 2. 心跳上报 (Heartbeat Report)

### 请求消息
**消息类型**: `device_heartbeat_report`

```typescript
{
  from: "device-id",
  to: "gateway", 
  type: "device_heartbeat_report",
  payload: {
    code: string,                   // 设备识别码
    cpu_usage?: number,             // CPU使用率 (0-100)
    memory_usage?: number,          // 内存使用率 (0-100)
    gpu_usage?: number,             // GPU使用率 (0-100)
    ip?: string,                    // IP地址
    timestamp?: string,             // 时间戳
    type?: string,                  // 设备类型
    model?: string,                 // 当前运行模型
    device_info?: {                 // 设备详细信息
      cpu_model?: string,
      cpu_cores?: number,
      cpu_threads?: number,
      ram_total?: number,           // 总内存 (GB)
      gpu_model?: string,
      gpu_count?: number,
      gpu_memory?: number,          // GPU显存 (GB)
      disk_total?: number,          // 总磁盘空间 (GB)
      os_info?: string
    },
    gateway_url?: string,
    device_id?: string
  }
}
```

### 响应消息
**消息类型**: `device_heartbeat_response`

```typescript
{
  from: "gateway",
  to: "device-id",
  type: "device_heartbeat_response", 
  payload: {
    success: boolean,               // 处理是否成功
    message?: string                // 响应消息
  }
}
```

### 示例代码
```javascript
// 发送心跳上报
const heartbeatMessage = {
  from: "my-device-id",
  to: "gateway",
  type: "device_heartbeat_report",
  payload: {
    code: "ABC123DEF456",
    cpu_usage: 45.2,
    memory_usage: 67.8,
    gpu_usage: 89.1,
    ip: "*************",
    timestamp: Date.now().toString(),
    type: "gpu-miner",
    model: "llama2:7b",
    device_info: {
      cpu_model: "Intel i7-12700K",
      cpu_cores: 12,
      cpu_threads: 20,
      ram_total: 32,
      gpu_model: "RTX 4090",
      gpu_count: 1,
      gpu_memory: 24,
      disk_total: 1000,
      os_info: "Ubuntu 22.04"
    }
  }
};

ws.send(JSON.stringify(heartbeatMessage));

// 建议每30-60秒发送一次心跳
setInterval(() => {
  ws.send(JSON.stringify(heartbeatMessage));
}, 30000);
```

## 3. 模型上报 (Model Report)

### 请求消息
**消息类型**: `device_model_report`

```typescript
{
  from: "device-id",
  to: "gateway",
  type: "device_model_report",
  payload: {
    device_id: string,              // 设备ID (UUID格式)
    models: Array<{                 // 模型列表
      name: string,                 // 模型名称
      modified_at: string,          // 修改时间
      size: number,                 // 模型大小 (字节)
      digest: string,               // 模型摘要
      details: {
        format: string,             // 模型格式
        family: string,             // 模型家族
        families: string[] | null,  // 模型家族列表
        parameter_size: string,     // 参数大小
        quantization_level: string  // 量化级别
      }
    }>
  }
}
```

### 响应消息
**消息类型**: `device_model_report_response`

```typescript
{
  from: "gateway",
  to: "device-id", 
  type: "device_model_report_response",
  payload: {
    success: boolean,               // 上报是否成功
    message?: string                // 响应消息
  }
}
```

### 示例代码
```javascript
// 发送模型上报
const modelReportMessage = {
  from: "my-device-id",
  to: "gateway",
  type: "device_model_report",
  payload: {
    device_id: "550e8400-e29b-41d4-a716-446655440000",
    models: [
      {
        name: "llama2:7b",
        modified_at: "2024-01-15T10:30:00Z",
        size: 3825819519,
        digest: "sha256:abc123def456...",
        details: {
          format: "gguf",
          family: "llama",
          families: ["llama"],
          parameter_size: "7B",
          quantization_level: "Q4_0"
        }
      },
      {
        name: "codellama:13b",
        modified_at: "2024-01-16T14:20:00Z", 
        size: 7365181440,
        digest: "sha256:def456ghi789...",
        details: {
          format: "gguf",
          family: "llama",
          families: ["llama"],
          parameter_size: "13B",
          quantization_level: "Q4_0"
        }
      }
    ]
  }
};

ws.send(JSON.stringify(modelReportMessage));
```

## 完整的节点对接示例

```javascript
class NodeClient {
  constructor(gatewayUrl, deviceId, deviceCode) {
    this.gatewayUrl = gatewayUrl;
    this.deviceId = deviceId;
    this.deviceCode = deviceCode;
    this.ws = null;
    this.isRegistered = false;
  }

  connect() {
    this.ws = new WebSocket(this.gatewayUrl);
    
    this.ws.onopen = () => {
      console.log('Connected to gateway');
      this.register();
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };

    this.ws.onclose = () => {
      console.log('Disconnected from gateway');
      // 重连逻辑
      setTimeout(() => this.connect(), 5000);
    };
  }

  register() {
    const message = {
      from: this.deviceId,
      to: "gateway",
      type: "device_register_request",
      payload: {
        code: this.deviceCode,
        gateway_address: "0x1234567890abcdef",
        reward_address: "0xfedcba0987654321",
        device_type: "gpu-miner",
        gpu_type: "RTX4090"
      }
    };
    
    this.ws.send(JSON.stringify(message));
  }

  startHeartbeat() {
    setInterval(() => {
      if (this.isRegistered) {
        this.sendHeartbeat();
      }
    }, 30000);
  }

  sendHeartbeat() {
    const message = {
      from: this.deviceId,
      to: "gateway",
      type: "device_heartbeat_report",
      payload: {
        code: this.deviceCode,
        cpu_usage: this.getCpuUsage(),
        memory_usage: this.getMemoryUsage(),
        gpu_usage: this.getGpuUsage(),
        timestamp: Date.now().toString()
      }
    };
    
    this.ws.send(JSON.stringify(message));
  }

  reportModels(models) {
    const message = {
      from: this.deviceId,
      to: "gateway", 
      type: "device_model_report",
      payload: {
        device_id: this.deviceId,
        models: models
      }
    };
    
    this.ws.send(JSON.stringify(message));
  }

  handleMessage(message) {
    switch (message.type) {
      case 'device_register_response':
        if (message.payload.success) {
          console.log('Registration successful');
          this.isRegistered = true;
          this.startHeartbeat();
        } else {
          console.error('Registration failed:', message.payload.error);
        }
        break;
        
      case 'device_heartbeat_response':
        console.log('Heartbeat acknowledged');
        break;
        
      case 'device_model_report_response':
        console.log('Model report acknowledged');
        break;
    }
  }

  // 系统监控方法 (需要根据实际环境实现)
  getCpuUsage() { /* 实现CPU使用率获取 */ }
  getMemoryUsage() { /* 实现内存使用率获取 */ }  
  getGpuUsage() { /* 实现GPU使用率获取 */ }
}

// 使用示例
const client = new NodeClient(
  'ws://gateway.example.com:8080',
  'my-device-id',
  'ABC123DEF456'
);

client.connect();
```

## 错误处理

### 常见错误码
- `Invalid code`: 注册码无效
- `Device not found`: 设备未找到
- `Failed to register device`: 设备注册失败
- `Failed to update heartbeat`: 心跳更新失败
- `Failed to report device models`: 模型上报失败

### 重连机制
建议实现指数退避重连策略：
```javascript
let reconnectDelay = 1000;
const maxDelay = 30000;

function reconnect() {
  setTimeout(() => {
    client.connect();
    reconnectDelay = Math.min(reconnectDelay * 2, maxDelay);
  }, reconnectDelay);
}
```

## 注意事项

1. **设备ID格式**: 建议使用UUID格式的设备ID
2. **心跳频率**: 建议30-60秒发送一次心跳，避免过于频繁
3. **模型上报时机**: 在模型列表发生变化时及时上报
4. **错误重试**: 实现适当的重试机制和错误处理
5. **连接保活**: 实现WebSocket连接的保活和重连机制

## 技术支持

如有问题，请联系技术支持团队或查看相关API文档。
