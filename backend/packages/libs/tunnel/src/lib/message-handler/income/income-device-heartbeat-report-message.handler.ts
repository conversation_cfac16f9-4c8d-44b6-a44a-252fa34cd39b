import { Inject, Injectable, Logger } from "@nestjs/common";
import { IncomeBaseMessageHandler } from "../base-message-handler";
import {
  DeviceHeartbeatReportMessageSchema,
  DeviceHeartbeatResponseMessageSchema,
  TunnelMessage
} from "@saito/models";
import { TunnelService } from "../../tunnel.interface";
import { MessageHandler } from "../message-handler.decorator";
import { NodeService } from "@saito/node";

@MessageHandler({ type: 'device_heartbeat_report', direction: 'income' })
@Injectable()
export class IncomeDeviceHeartbeatReportMessageHandler extends IncomeBaseMessageHandler {
  private readonly logger = new Logger(IncomeDeviceHeartbeatReportMessageHandler.name);

  constructor(
    @Inject('TunnelService') private readonly tunnel: TunnelService,
    @Inject('PEER_ID') protected override readonly peerId: string,
    private readonly nodeService: NodeService
  ) {
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    try {
      const heartbeatMessage = DeviceHeartbeatReportMessageSchema.parse(message);
      this.logger.log(`[IncomeDeviceHeartbeatReportHandler] Processing heartbeat from ${message.from}`);

      // 调用现有的心跳更新服务
      await this.nodeService.updateDeviceHeartbeatNew(heartbeatMessage.payload);

      // 构建响应消息
      const responseMessage = DeviceHeartbeatResponseMessageSchema.parse({
        type: 'device_heartbeat_response',
        from: this.peerId,
        to: heartbeatMessage.from,
        payload: {
          success: true,
          message: 'Heartbeat processed successfully'
        }
      });

      // 发送响应
      await this.tunnel.handleMessage(responseMessage);
      
      this.logger.log(`[IncomeDeviceHeartbeatReportHandler] Heartbeat processed successfully for device from ${message.from}`);
    } catch (error) {
      this.logger.error(`[IncomeDeviceHeartbeatReportHandler] Failed to process heartbeat: ${error}`);
      
      // 发送错误响应
      const errorResponse = DeviceHeartbeatResponseMessageSchema.parse({
        type: 'device_heartbeat_response',
        from: this.peerId,
        to: message.from,
        payload: {
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      await this.tunnel.handleMessage(errorResponse);
    }
  }
}
