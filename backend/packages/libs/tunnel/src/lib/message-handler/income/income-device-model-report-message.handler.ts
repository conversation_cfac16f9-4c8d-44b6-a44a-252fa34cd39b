import { Inject, Injectable, Logger } from "@nestjs/common";
import { IncomeBaseMessageHandler } from "../base-message-handler";
import {
  DeviceModelReportMessageSchema,
  DeviceModelReportResponseMessageSchema,
  TunnelMessage
} from "@saito/models";
import { TunnelService } from "../../tunnel.interface";
import { MessageHandler } from "../message-handler.decorator";
import { NodeService } from "@saito/node";

@MessageHandler({ type: 'device_model_report', direction: 'income' })
@Injectable()
export class IncomeDeviceModelReportMessageHandler extends IncomeBaseMessageHandler {
  private readonly logger = new Logger(IncomeDeviceModelReportMessageHandler.name);

  constructor(
    @Inject('TunnelService') private readonly tunnel: TunnelService,
    @Inject('PEER_ID') protected override readonly peerId: string,
    private readonly nodeService: NodeService
  ) {
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    try {
      const modelReportMessage = DeviceModelReportMessageSchema.parse(message);
      this.logger.log(`[IncomeDeviceModelReportHandler] Processing model report from ${message.from}`);

      // 调用现有的模型上报服务
      const reportResult = await this.nodeService.reportDeviceModels(modelReportMessage.payload);

      // 构建响应消息
      const responseMessage = DeviceModelReportResponseMessageSchema.parse({
        type: 'device_model_report_response',
        from: this.peerId,
        to: modelReportMessage.from,
        payload: reportResult
      });

      // 发送响应
      await this.tunnel.handleMessage(responseMessage);
      
      this.logger.log(`[IncomeDeviceModelReportHandler] Model report processed successfully for device ${modelReportMessage.payload.device_id}`);
    } catch (error) {
      this.logger.error(`[IncomeDeviceModelReportHandler] Failed to process model report: ${error}`);
      
      // 发送错误响应
      const errorResponse = DeviceModelReportResponseMessageSchema.parse({
        type: 'device_model_report_response',
        from: this.peerId,
        to: message.from,
        payload: {
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      await this.tunnel.handleMessage(errorResponse);
    }
  }
}
